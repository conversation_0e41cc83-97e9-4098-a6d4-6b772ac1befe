apiVersion: v1
kind: Service
metadata:
  name: zookeeper-service
  namespace: coffee-system
spec:
  selector:
    app: zookeeper
  ports:
  - port: 2181
    targetPort: 2181
  clusterIP: None
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zookeeper
  namespace: coffee-system
spec:
  serviceName: zookeeper-service
  replicas: 1
  selector:
    matchLabels:
      app: zookeeper
  template:
    metadata:
      labels:
        app: zookeeper
    spec:
      containers:
      - name: zookeeper
        image: confluentinc/cp-zookeeper:latest
        ports:
        - containerPort: 2181
        env:
        - name: ZOOKEEPER_CLIENT_PORT
          value: "2181"
        - name: ZOOKEEPER_TICK_TIME
          value: "2000"
        resources:
          requests:
            memory: "256Mi"
            cpu: "100m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        readinessProbe:
          tcpSocket:
            port: 2181
          initialDelaySeconds: 10
          periodSeconds: 10
        livenessProbe:
          tcpSocket:
            port: 2181
          initialDelaySeconds: 30
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: kafka-service
  namespace: coffee-system
spec:
  selector:
    app: kafka
  ports:
  - port: 9092
    targetPort: 9092
  clusterIP: None
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: kafka
  namespace: coffee-system
spec:
  serviceName: kafka-service
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
    spec:
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:latest
        ports:
        - containerPort: 9092
        env:
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: KAFKA_ZOOKEEPER_CONNECT
          value: "zookeeper-service:2181"
        - name: KAFKA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka-service:9092"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_AUTO_CREATE_TOPICS_ENABLE
          value: "true"
        resources:
          requests:
            memory: "512Mi"
            cpu: "200m"
          limits:
            memory: "1Gi"
            cpu: "1000m"
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - kafka-topics --bootstrap-server localhost:9092 --list
          initialDelaySeconds: 30
          periodSeconds: 10
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - kafka-topics --bootstrap-server localhost:9092 --list
          initialDelaySeconds: 60
          periodSeconds: 10
---
apiVersion: batch/v1
kind: Job
metadata:
  name: kafka-setup
  namespace: coffee-system
spec:
  template:
    spec:
      containers:
      - name: kafka-setup
        image: confluentinc/cp-kafka:latest
        command:
        - sh
        - -c
        - |
          echo "Waiting for Kafka to be ready..."
          until kafka-topics --bootstrap-server kafka-service:9092 --list; do
            echo "Kafka not ready yet, waiting..."
            sleep 5
          done
          echo "Creating topics..."
          kafka-topics --bootstrap-server kafka-service:9092 --create --if-not-exists --topic coffee_orders --partitions 3 --replication-factor 1
          kafka-topics --bootstrap-server kafka-service:9092 --create --if-not-exists --topic processed_orders --partitions 3 --replication-factor 1
          echo "Topics created."
          kafka-topics --bootstrap-server kafka-service:9092 --list
      restartPolicy: OnFailure
