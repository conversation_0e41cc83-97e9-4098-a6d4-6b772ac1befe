# Coffee Order System Diagrams

This directory contains diagrams that illustrate the architecture and operation of the Coffee Order System.

## List of Diagrams

1. [Architecture Diagram](architecture.md) - shows the overall system architecture and relationships between components.
2. [Sequence Diagram](sequence.md) - shows the sequence of interactions between system components.
3. [Component Diagram](component.md) - shows the system structure and relationships between components.
4. [Deployment Diagram](deployment.md) - shows how the system can be deployed in a production environment.

## How to View Diagrams

The diagrams are created using Mermaid syntax. To view them, you can use:

1. **GitHub** - GitHub supports rendering Mermaid diagrams directly in Markdown files.
2. **Mermaid Live Editor** - [Mermaid Live Editor](https://mermaid.live/) allows you to edit and view Mermaid diagrams.
3. **VS Code with Mermaid Extension** - The Mermaid extension for VS Code allows you to view Mermaid diagrams directly in the editor.

## How to Update Diagrams

To update diagrams:

1. Open the corresponding Markdown file.
2. Edit the Mermaid code.
3. Save the file.

## Exporting Diagrams

To export diagrams to PNG or SVG format:

1. Open the diagram in [Mermaid Live Editor](https://mermaid.live/).
2. Click the "Export" button and select the desired format.
3. Save the file in the `images` directory.
