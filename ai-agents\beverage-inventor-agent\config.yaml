# Configuration for Beverage Inventor Agent

# LLM Integration (Optional)
llm:
  provider: "ollama" # e.g., "ollama", "gemini", "openai"
  api_key: "" # Your API key if required by the provider
  model: "llama3" # e.g., "llama3", "gemini-pro", "gpt-4"
  endpoint: "http://localhost:11434" # Ollama endpoint or other LLM API endpoint

# Ingredient Data Source (Simulation for now)
ingredients:
  - name: "Dragon Fruit"
    source: "Mars Base Report"
  - name: "Moon Rock Dust"
    source: "Lunar Mining Corp"
  - name: "Andromeda Berry"
    source: "Interstellar Trade Federation"

# Kafka Integration
kafka:
  enabled: true
  broker_address: "localhost:9092" # Kafka broker address
  output_topic: "beverage_ideas" # Topic for new beverage ideas

# Task Manager Agent Integration (Optional) - Disabled as Kafka is used for communication
task_manager:
  enabled: false
  api_endpoint: "http://localhost:8080/api/v1/tasks" # Example endpoint for Task Manager Agent
  api_key: "" # API key for Task Manager Agent if required