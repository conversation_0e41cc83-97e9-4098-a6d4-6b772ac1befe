package accounts

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"time"

	"golang.org/x/crypto/bcrypt"
	"github.com/google/uuid"
	
	"github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>/go-coffee/web3-wallet-backend/pkg/config"
	"github.com/Di<PERSON><PERSON><PERSON>ti/go-coffee/web3-wallet-backend/pkg/logger"
	"github.com/Dima<PERSON><PERSON>ti/go-coffee/web3-wallet-backend/pkg/redis"
)

// Service defines the interface for account business logic
type Service interface {
	// Account management
	CreateAccount(ctx context.Context, req *CreateAccountRequest) (*Account, error)
	GetAccount(ctx context.Context, id string) (*Account, error)
	GetAccountByEmail(ctx context.Context, email string) (*Account, error)
	UpdateAccount(ctx context.Context, id string, req *UpdateAccountRequest) (*Account, error)
	DeleteAccount(ctx context.Context, id string) error
	ListAccounts(ctx context.Context, req *AccountListRequest) (*AccountListResponse, error)
	
	// Authentication
	Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error)
	Logout(ctx context.Context, sessionToken string) error
	RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error)
	ChangePassword(ctx context.Context, accountID string, req *ChangePasswordRequest) error
	ResetPassword(ctx context.Context, req *ResetPasswordRequest) error
	ConfirmPasswordReset(ctx context.Context, req *ConfirmPasswordResetRequest) error
	
	// Two-factor authentication
	EnableTwoFactor(ctx context.Context, accountID string, req *EnableTwoFactorRequest) error
	DisableTwoFactor(ctx context.Context, accountID string) error
	VerifyTwoFactor(ctx context.Context, accountID string, req *VerifyTwoFactorRequest) error
	
	// KYC management
	SubmitKYCDocument(ctx context.Context, accountID string, req *KYCSubmissionRequest) (*KYCDocument, error)
	GetKYCDocuments(ctx context.Context, accountID string) ([]KYCDocument, error)
	UpdateKYCStatus(ctx context.Context, accountID string, status KYCStatus, level KYCLevel) error
	
	// Security
	GetSecurityEvents(ctx context.Context, accountID string) ([]SecurityEvent, error)
	LogSecurityEvent(ctx context.Context, accountID string, eventType SecurityEventType, severity SecuritySeverity, description string, metadata map[string]interface{}) error
	
	// Session management
	ValidateSession(ctx context.Context, sessionToken string) (*Session, error)
	InvalidateSession(ctx context.Context, sessionToken string) error
	CleanupExpiredSessions(ctx context.Context) error
}

// AccountService implements the Service interface
type AccountService struct {
	repo   Repository
	config config.AccountsConfig
	logger *logger.Logger
	cache  redis.Client
}

// NewService creates a new account service
func NewService(repo Repository, cfg config.AccountsConfig, logger *logger.Logger, cache redis.Client) Service {
	return &AccountService{
		repo:   repo,
		config: cfg,
		logger: logger,
		cache:  cache,
	}
}

// CreateAccount creates a new account
func (s *AccountService) CreateAccount(ctx context.Context, req *CreateAccountRequest) (*Account, error) {
	s.logger.Info("Creating new account", "email", req.Email)

	// Check if account already exists
	existingAccount, err := s.repo.GetAccountByEmail(ctx, req.Email)
	if err == nil && existingAccount != nil {
		return nil, fmt.Errorf("account with email %s already exists", req.Email)
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create account
	account := &Account{
		ID:            uuid.New().String(),
		UserID:        uuid.New().String(),
		Email:         req.Email,
		Phone:         req.Phone,
		FirstName:     req.FirstName,
		LastName:      req.LastName,
		Country:       req.Country,
		AccountType:   req.AccountType,
		AccountStatus: AccountStatusPending,
		KYCStatus:     KYCStatusNotStarted,
		KYCLevel:      KYCLevelNone,
		RiskScore:     0.0,
		ComplianceFlags: []string{},
		TwoFactorEnabled: false,
		FailedLoginCount: 0,
		AccountLimits: AccountLimits{
			DailyTransactionLimit:   s.config.AccountLimits.DailyTransactionLimit,
			MonthlyTransactionLimit: s.config.AccountLimits.MonthlyTransactionLimit,
			MaxWallets:              s.config.AccountLimits.MaxWalletsPerUser,
			MaxCards:                s.config.AccountLimits.MaxCardsPerUser,
		},
		NotificationPrefs: NotificationPreferences{
			EmailEnabled:      s.config.NotificationSettings.EmailEnabled,
			SMSEnabled:        s.config.NotificationSettings.SMSEnabled,
			PushEnabled:       s.config.NotificationSettings.PushEnabled,
			SecurityAlerts:    s.config.NotificationSettings.SecurityAlerts,
			TransactionAlerts: s.config.NotificationSettings.TransactionAlerts,
		},
		Metadata:  make(map[string]interface{}),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Store hashed password in metadata (in production, use separate table)
	account.Metadata["password_hash"] = string(hashedPassword)

	err = s.repo.CreateAccount(ctx, account)
	if err != nil {
		return nil, fmt.Errorf("failed to create account: %w", err)
	}

	// Log security event
	s.LogSecurityEvent(ctx, account.ID, SecurityEventTypeLogin, SecuritySeverityLow, "Account created", nil)

	s.logger.Info("Account created successfully", "account_id", account.ID, "email", account.Email)
	return account, nil
}

// GetAccount retrieves an account by ID
func (s *AccountService) GetAccount(ctx context.Context, id string) (*Account, error) {
	account, err := s.repo.GetAccountByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// Remove sensitive data
	account.Metadata = s.sanitizeMetadata(account.Metadata)
	
	return account, nil
}

// GetAccountByEmail retrieves an account by email
func (s *AccountService) GetAccountByEmail(ctx context.Context, email string) (*Account, error) {
	account, err := s.repo.GetAccountByEmail(ctx, email)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// Remove sensitive data
	account.Metadata = s.sanitizeMetadata(account.Metadata)
	
	return account, nil
}

// UpdateAccount updates an existing account
func (s *AccountService) UpdateAccount(ctx context.Context, id string, req *UpdateAccountRequest) (*Account, error) {
	s.logger.Info("Updating account", "account_id", id)

	account, err := s.repo.GetAccountByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get account: %w", err)
	}

	// Update fields if provided
	if req.FirstName != nil {
		account.FirstName = *req.FirstName
	}
	if req.LastName != nil {
		account.LastName = *req.LastName
	}
	if req.Phone != nil {
		account.Phone = *req.Phone
	}
	if req.Country != nil {
		account.Country = *req.Country
	}
	if req.State != nil {
		account.State = *req.State
	}
	if req.City != nil {
		account.City = *req.City
	}
	if req.Address != nil {
		account.Address = *req.Address
	}
	if req.PostalCode != nil {
		account.PostalCode = *req.PostalCode
	}
	if req.NotificationPrefs != nil {
		account.NotificationPrefs = *req.NotificationPrefs
	}

	err = s.repo.UpdateAccount(ctx, account)
	if err != nil {
		return nil, fmt.Errorf("failed to update account: %w", err)
	}

	// Remove sensitive data
	account.Metadata = s.sanitizeMetadata(account.Metadata)

	s.logger.Info("Account updated successfully", "account_id", id)
	return account, nil
}

// DeleteAccount deletes an account
func (s *AccountService) DeleteAccount(ctx context.Context, id string) error {
	s.logger.Info("Deleting account", "account_id", id)

	err := s.repo.DeleteAccount(ctx, id)
	if err != nil {
		return fmt.Errorf("failed to delete account: %w", err)
	}

	s.logger.Info("Account deleted successfully", "account_id", id)
	return nil
}

// ListAccounts retrieves a list of accounts
func (s *AccountService) ListAccounts(ctx context.Context, req *AccountListRequest) (*AccountListResponse, error) {
	// Set defaults
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 20
	}

	accounts, total, err := s.repo.ListAccounts(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to list accounts: %w", err)
	}

	// Remove sensitive data from all accounts
	for i := range accounts {
		accounts[i].Metadata = s.sanitizeMetadata(accounts[i].Metadata)
	}

	totalPages := (total + req.Limit - 1) / req.Limit

	return &AccountListResponse{
		Accounts:   accounts,
		Total:      total,
		Page:       req.Page,
		Limit:      req.Limit,
		TotalPages: totalPages,
	}, nil
}

// Login authenticates a user and creates a session
func (s *AccountService) Login(ctx context.Context, req *LoginRequest) (*LoginResponse, error) {
	s.logger.Info("User login attempt", "email", req.Email)

	// Get account
	account, err := s.repo.GetAccountByEmail(ctx, req.Email)
	if err != nil {
		s.LogSecurityEvent(ctx, "", SecurityEventTypeFailedLogin, SecuritySeverityMedium, "Login attempt with invalid email", map[string]interface{}{"email": req.Email})
		return nil, fmt.Errorf("invalid credentials")
	}

	// Check account status
	if account.AccountStatus != AccountStatusActive {
		s.LogSecurityEvent(ctx, account.ID, SecurityEventTypeFailedLogin, SecuritySeverityMedium, "Login attempt on inactive account", nil)
		return nil, fmt.Errorf("account is not active")
	}

	// Check failed login attempts
	if account.FailedLoginCount >= s.config.MaxLoginAttempts {
		s.LogSecurityEvent(ctx, account.ID, SecurityEventTypeAccountLocked, SecuritySeverityHigh, "Account locked due to too many failed login attempts", nil)
		return nil, fmt.Errorf("account is locked due to too many failed login attempts")
	}

	// Verify password
	passwordHash, ok := account.Metadata["password_hash"].(string)
	if !ok {
		return nil, fmt.Errorf("invalid account data")
	}

	err = bcrypt.CompareHashAndPassword([]byte(passwordHash), []byte(req.Password))
	if err != nil {
		// Increment failed login count
		account.FailedLoginCount++
		s.repo.UpdateAccount(ctx, account)
		
		s.LogSecurityEvent(ctx, account.ID, SecurityEventTypeFailedLogin, SecuritySeverityMedium, "Invalid password", nil)
		return nil, fmt.Errorf("invalid credentials")
	}

	// Reset failed login count on successful login
	account.FailedLoginCount = 0
	account.LastLoginAt = &[]time.Time{time.Now()}[0]
	s.repo.UpdateAccount(ctx, account)

	// Create session
	session := &Session{
		ID:           uuid.New().String(),
		AccountID:    account.ID,
		DeviceID:     req.DeviceID,
		SessionToken: s.generateToken(),
		RefreshToken: s.generateToken(),
		ExpiresAt:    time.Now().Add(24 * time.Hour), // 24 hours
		IsActive:     true,
		Metadata:     make(map[string]interface{}),
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if req.RememberMe {
		session.ExpiresAt = time.Now().Add(30 * 24 * time.Hour) // 30 days
	}

	err = s.repo.CreateSession(ctx, session)
	if err != nil {
		return nil, fmt.Errorf("failed to create session: %w", err)
	}

	// Log successful login
	s.LogSecurityEvent(ctx, account.ID, SecurityEventTypeLogin, SecuritySeverityLow, "Successful login", nil)

	// Remove sensitive data
	account.Metadata = s.sanitizeMetadata(account.Metadata)

	s.logger.Info("User logged in successfully", "account_id", account.ID, "email", account.Email)

	return &LoginResponse{
		Account:      account,
		AccessToken:  session.SessionToken,
		RefreshToken: session.RefreshToken,
		ExpiresIn:    int64(session.ExpiresAt.Sub(time.Now()).Seconds()),
		TokenType:    "Bearer",
	}, nil
}

// Logout invalidates a session
func (s *AccountService) Logout(ctx context.Context, sessionToken string) error {
	session, err := s.repo.GetSessionByToken(ctx, sessionToken)
	if err != nil {
		return fmt.Errorf("invalid session")
	}

	err = s.repo.DeleteSession(ctx, session.ID)
	if err != nil {
		return fmt.Errorf("failed to logout: %w", err)
	}

	s.logger.Info("User logged out", "account_id", session.AccountID)
	return nil
}

// ValidateSession validates a session token
func (s *AccountService) ValidateSession(ctx context.Context, sessionToken string) (*Session, error) {
	session, err := s.repo.GetSessionByToken(ctx, sessionToken)
	if err != nil {
		return nil, fmt.Errorf("invalid session")
	}

	return session, nil
}

// LogSecurityEvent logs a security event
func (s *AccountService) LogSecurityEvent(ctx context.Context, accountID string, eventType SecurityEventType, severity SecuritySeverity, description string, metadata map[string]interface{}) error {
	event := &SecurityEvent{
		ID:          uuid.New().String(),
		AccountID:   accountID,
		EventType:   eventType,
		Severity:    severity,
		Description: description,
		Resolved:    false,
		Metadata:    metadata,
		CreatedAt:   time.Now(),
	}

	return s.repo.CreateSecurityEvent(ctx, event)
}

// GetSecurityEvents retrieves security events for an account
func (s *AccountService) GetSecurityEvents(ctx context.Context, accountID string) ([]SecurityEvent, error) {
	return s.repo.GetSecurityEvents(ctx, accountID, 50)
}

// Helper methods

// generateToken generates a random token
func (s *AccountService) generateToken() string {
	bytes := make([]byte, 32)
	rand.Read(bytes)
	return hex.EncodeToString(bytes)
}

// sanitizeMetadata removes sensitive data from metadata
func (s *AccountService) sanitizeMetadata(metadata map[string]interface{}) map[string]interface{} {
	sanitized := make(map[string]interface{})
	for k, v := range metadata {
		if k != "password_hash" {
			sanitized[k] = v
		}
	}
	return sanitized
}

// Placeholder implementations for remaining methods
func (s *AccountService) RefreshToken(ctx context.Context, refreshToken string) (*LoginResponse, error) {
	// TODO: Implement refresh token logic
	return nil, fmt.Errorf("not implemented")
}

func (s *AccountService) ChangePassword(ctx context.Context, accountID string, req *ChangePasswordRequest) error {
	// TODO: Implement password change logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) ResetPassword(ctx context.Context, req *ResetPasswordRequest) error {
	// TODO: Implement password reset logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) ConfirmPasswordReset(ctx context.Context, req *ConfirmPasswordResetRequest) error {
	// TODO: Implement password reset confirmation logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) EnableTwoFactor(ctx context.Context, accountID string, req *EnableTwoFactorRequest) error {
	// TODO: Implement 2FA enable logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) DisableTwoFactor(ctx context.Context, accountID string) error {
	// TODO: Implement 2FA disable logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) VerifyTwoFactor(ctx context.Context, accountID string, req *VerifyTwoFactorRequest) error {
	// TODO: Implement 2FA verification logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) SubmitKYCDocument(ctx context.Context, accountID string, req *KYCSubmissionRequest) (*KYCDocument, error) {
	// TODO: Implement KYC document submission logic
	return nil, fmt.Errorf("not implemented")
}

func (s *AccountService) GetKYCDocuments(ctx context.Context, accountID string) ([]KYCDocument, error) {
	return s.repo.GetKYCDocuments(ctx, accountID)
}

func (s *AccountService) UpdateKYCStatus(ctx context.Context, accountID string, status KYCStatus, level KYCLevel) error {
	// TODO: Implement KYC status update logic
	return fmt.Errorf("not implemented")
}

func (s *AccountService) InvalidateSession(ctx context.Context, sessionToken string) error {
	return s.Logout(ctx, sessionToken)
}

func (s *AccountService) CleanupExpiredSessions(ctx context.Context) error {
	return s.repo.DeleteExpiredSessions(ctx)
}
