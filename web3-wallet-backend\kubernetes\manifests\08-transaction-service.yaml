apiVersion: apps/v1
kind: Deployment
metadata:
  name: transaction-service
  namespace: web3-wallet
spec:
  replicas: 2
  selector:
    matchLabels:
      app: transaction-service
  template:
    metadata:
      labels:
        app: transaction-service
    spec:
      containers:
      - name: transaction-service
        image: yourusername/web3-wallet-transaction-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 50052
          name: grpc
        envFrom:
        - configMapRef:
            name: web3-wallet-config
        - secretRef:
            name: web3-wallet-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50052
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50052
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: transaction-service
  namespace: web3-wallet
spec:
  selector:
    app: transaction-service
  ports:
  - port: 50052
    targetPort: 50052
    name: grpc
  type: ClusterIP
