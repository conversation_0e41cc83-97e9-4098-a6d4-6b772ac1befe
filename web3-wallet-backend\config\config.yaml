# Web3 Wallet Backend Configuration

# Server Configuration
server:
  port: 8080
  host: "0.0.0.0"
  timeout: 30s
  read_timeout: 15s
  write_timeout: 15s
  max_header_bytes: 1048576 # 1MB

# Database Configuration
database:
  driver: "postgres"
  host: "localhost"
  port: 5432
  username: "postgres"
  password: "postgres"
  database: "web3_wallet"
  ssl_mode: "disable"
  max_open_conns: 25
  max_idle_conns: 25
  conn_max_lifetime: "5m"

# Redis Configuration
redis:
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# Blockchain Configuration
blockchain:
  # Ethereum Configuration
  ethereum:
    network: "mainnet"
    rpc_url: "https://mainnet.infura.io/v3/YOUR_INFURA_KEY"
    ws_url: "wss://mainnet.infura.io/ws/v3/YOUR_INFURA_KEY"
    chain_id: 1
    gas_limit: 21000
    gas_price: "auto"
    confirmation_blocks: 12

  # Binance Smart Chain Configuration
  bsc:
    network: "mainnet"
    rpc_url: "https://bsc-dataseed.binance.org/"
    ws_url: "wss://bsc-ws-node.nariox.org:443"
    chain_id: 56
    gas_limit: 21000
    gas_price: "auto"
    confirmation_blocks: 15

  # Polygon Configuration
  polygon:
    network: "mainnet"
    rpc_url: "https://polygon-rpc.com"
    ws_url: "wss://polygon-ws.nariox.org:443"
    chain_id: 137
    gas_limit: 21000
    gas_price: "auto"
    confirmation_blocks: 64

  # Solana Configuration
  solana:
    network: "mainnet-beta"
    rpc_url: "https://api.mainnet-beta.solana.com"
    ws_url: "wss://api.mainnet-beta.solana.com"
    cluster: "mainnet-beta"
    commitment: "confirmed"
    timeout: "30s"
    max_retries: 3
    confirmation_blocks: 32

# Security Configuration
security:
  jwt:
    secret: "your-secret-key-here"
    expiration: "24h"
    refresh_expiration: "168h"
  encryption:
    key_derivation: "pbkdf2"
    iterations: 10000
    salt_length: 16
    key_length: 32
  rate_limit:
    enabled: true
    requests_per_minute: 60
    burst: 100

# Logging Configuration
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "logs/web3-wallet.log"
  max_size: 100 # MB
  max_age: 30 # days
  max_backups: 10
  compress: true

# Monitoring Configuration
monitoring:
  prometheus:
    enabled: true
    port: 9090
  health_check:
    enabled: true
    endpoint: "/health"
  metrics:
    enabled: true
    endpoint: "/metrics"

# Notification Configuration
notification:
  email:
    enabled: true
    smtp_host: "smtp.example.com"
    smtp_port: 587
    smtp_username: "<EMAIL>"
    smtp_password: "your-password"
    from_email: "<EMAIL>"
    from_name: "Web3 Wallet"
  sms:
    enabled: false
    provider: "twilio"
    account_sid: "your-account-sid"
    auth_token: "your-auth-token"
    from_number: "+**********"
  push:
    enabled: true
    provider: "firebase"
    credentials_file: "firebase-credentials.json"

# DeFi Protocol Configuration
defi:
  # Uniswap Configuration
  uniswap:
    enabled: true
    factory_address: "******************************************"
    router_address: "******************************************"
    quoter_address: "******************************************"
    position_manager: "******************************************"
    default_slippage: 0.005  # 0.5%
    default_deadline: 1800   # 30 minutes

  # Aave Configuration
  aave:
    enabled: true
    pool_address: "******************************************"
    data_provider_address: "******************************************"
    oracle_address: "******************************************"
    rewards_controller: "******************************************"

  # Chainlink Configuration
  chainlink:
    enabled: true
    price_feeds:
      "ETH/USD": "******************************************"
      "BTC/USD": "******************************************"
      "USDC/USD": "******************************************"
      "USDT/USD": "******************************************"
      "LINK/USD": "******************************************"
      "UNI/USD": "******************************************"
      "AAVE/USD": "******************************************"

  # 1inch Configuration
  oneinch:
    enabled: true
    api_key: "YOUR_1INCH_API_KEY"
    base_url: "https://api.1inch.io/v5.0"

  # Coffee Token Configuration
  coffee:
    enabled: true
    token_addresses:
      ethereum: "******************************************"  # To be updated after deployment
      bsc: "******************************************"      # To be updated after deployment
      polygon: "******************************************"   # To be updated after deployment
    staking_contract: "******************************************"  # To be updated after deployment
    rewards_apy: 0.12  # 12% APY
    min_stake_amount: "100"  # 100 COFFEE tokens

# Services Configuration
services:
  api_gateway:
    host: "localhost"
    http_port: 8080
    grpc_port: 50050
    enabled: true

  wallet_service:
    host: "localhost"
    http_port: 8081
    grpc_port: 50051
    enabled: true

  transaction_service:
    host: "localhost"
    http_port: 8082
    grpc_port: 50052
    enabled: true

  smart_contract_service:
    host: "localhost"
    http_port: 8083
    grpc_port: 50053
    enabled: true

  security_service:
    host: "localhost"
    http_port: 8084
    grpc_port: 50054
    enabled: true

  defi_service:
    host: "localhost"
    http_port: 8085
    grpc_port: 50055
    enabled: true

  telegram_bot:
    host: "localhost"
    http_port: 8087
    grpc_port: 50057
    enabled: true

# Telegram Bot Configuration
telegram:
  enabled: true
  bot_token: "${TELEGRAM_BOT_TOKEN}"
  webhook_url: "${TELEGRAM_WEBHOOK_URL}"
  webhook_port: 8087
  webhook_path: "/webhook"
  debug: true
  timeout: 30
  max_connections: 100
  allowed_updates:
    - "message"
    - "callback_query"
    - "inline_query"
  commands:
    - command: "start"
      description: "Почати роботу з ботом"
    - command: "wallet"
      description: "Управління гаманцем"
    - command: "balance"
      description: "Перевірити баланс"
    - command: "pay"
      description: "Здійснити платіж"
    - command: "coffee"
      description: "Замовити каву"
    - command: "menu"
      description: "Переглянути меню"
    - command: "orders"
      description: "Мої замовлення"
    - command: "help"
      description: "Допомога"
    - command: "settings"
      description: "Налаштування"

# AI Configuration
ai:
  enabled: true

  # LangChain Configuration
  langchain:
    enabled: true
    model: "gpt-3.5-turbo"
    temperature: 0.7
    max_tokens: 1000
    timeout: 30

  # Gemini Configuration
  gemini:
    enabled: true
    api_key: "${GEMINI_API_KEY}"
    model: "gemini-1.5-flash"
    temperature: 0.7
    max_tokens: 1000
    timeout: 30
    safety_settings:
      harassment: "BLOCK_MEDIUM_AND_ABOVE"
      hate_speech: "BLOCK_MEDIUM_AND_ABOVE"
      sexually_explicit: "BLOCK_MEDIUM_AND_ABOVE"
      dangerous_content: "BLOCK_MEDIUM_AND_ABOVE"

  # Ollama Configuration
  ollama:
    enabled: true
    host: "localhost"
    port: 11434
    model: "llama3.1"
    temperature: 0.7
    timeout: 60
    keep_alive: "5m"

  # AI Service Configuration
  service:
    default_provider: "gemini"
    fallback_provider: "ollama"
    cache_enabled: true
    cache_ttl: "1h"
    rate_limit:
      requests_per_minute: 60
      burst: 10
