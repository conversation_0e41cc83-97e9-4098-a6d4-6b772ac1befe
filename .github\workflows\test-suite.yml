name: Comprehensive Test Suite

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:

env:
  GO_VERSION: '1.21'

jobs:
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    strategy:
      matrix:
        service:
          - name: producer
            path: ./producer
          - name: consumer
            path: ./consumer
          - name: streams
            path: ./streams
          - name: web3-wallet-backend
            path: ./web3-wallet-backend
          - name: accounts-service
            path: ./accounts-service

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Cache Go modules
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/go-build
            ~/go/pkg/mod
          key: ${{ runner.os }}-go-${{ matrix.service.name }}-${{ hashFiles(format('{0}/go.sum', matrix.service.path)) }}
          restore-keys: |
            ${{ runner.os }}-go-${{ matrix.service.name }}-

      - name: Install dependencies
        run: |
          cd ${{ matrix.service.path }}
          go mod download

      - name: Run unit tests
        run: |
          cd ${{ matrix.service.path }}
          go test -v -race -coverprofile=coverage.out ./...

      - name: Generate coverage report
        run: |
          cd ${{ matrix.service.path }}
          go tool cover -html=coverage.out -o coverage.html

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        with:
          file: ${{ matrix.service.path }}/coverage.out
          flags: ${{ matrix.service.name }}
          name: ${{ matrix.service.name }}-coverage

      - name: Upload coverage artifacts
        uses: actions/upload-artifact@v4
        with:
          name: coverage-${{ matrix.service.name }}
          path: ${{ matrix.service.path }}/coverage.html

  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: test_db
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      kafka:
        image: confluentinc/cp-kafka:latest
        env:
          KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
          KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:9092
          KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
        ports:
          - 9092:9092

      zookeeper:
        image: confluentinc/cp-zookeeper:latest
        env:
          ZOOKEEPER_CLIENT_PORT: 2181
          ZOOKEEPER_TICK_TIME: 2000
        ports:
          - 2181:2181

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Wait for services
        run: |
          # Wait for Kafka to be ready
          timeout 60 bash -c 'until nc -z localhost 9092; do sleep 1; done'
          echo "Kafka is ready"

          # Wait for Redis to be ready
          timeout 30 bash -c 'until nc -z localhost 6379; do sleep 1; done'
          echo "Redis is ready"

          # Wait for PostgreSQL to be ready
          timeout 30 bash -c 'until nc -z localhost 5432; do sleep 1; done'
          echo "PostgreSQL is ready"

      - name: Run Web3 Wallet Backend integration tests
        env:
          REDIS_HOST: localhost
          REDIS_PORT: 6379
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: test_db
          DB_USER: test_user
          DB_PASSWORD: test_password
          KAFKA_BROKERS: localhost:9092
        run: |
          cd web3-wallet-backend
          go test -v -tags=integration ./tests/integration/...

      - name: Run Kafka services integration tests
        env:
          KAFKA_BROKERS: localhost:9092
          REDIS_HOST: localhost
          REDIS_PORT: 6379
        run: |
          # Test producer
          cd producer
          go test -v -tags=integration ./...

          # Test consumer
          cd ../consumer
          go test -v -tags=integration ./...

          # Test streams
          cd ../streams
          go test -v -tags=integration ./...

  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'schedule' || github.event_name == 'workflow_dispatch'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run benchmark tests
        run: |
          cd web3-wallet-backend
          go test -bench=. -benchmem -run=^$ ./... > benchmark_results.txt

      - name: Upload benchmark results
        uses: actions/upload-artifact@v4
        with:
          name: benchmark-results
          path: web3-wallet-backend/benchmark_results.txt

  e2e-tests:
    name: End-to-End Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Compose
        run: |
          cd web3-wallet-backend/deployments/telegram-bot
          docker-compose up -d redis postgres

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Run E2E tests
        env:
          TELEGRAM_BOT_TOKEN: ${{ secrets.TELEGRAM_BOT_TOKEN_TEST }}
          GEMINI_API_KEY: ${{ secrets.GEMINI_API_KEY_TEST }}
          REDIS_HOST: localhost
          REDIS_PORT: 6379
        run: |
          cd web3-wallet-backend
          go test -v -tags=e2e ./tests/e2e/...

      - name: Cleanup
        if: always()
        run: |
          cd web3-wallet-backend/deployments/telegram-bot
          docker-compose down -v

  security-tests:
    name: Security Tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: ${{ env.GO_VERSION }}

      - name: Install Gosec
        run: |
          curl -sfL https://raw.githubusercontent.com/securecodewarrior/gosec/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.18.2
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH

      - name: Run Gosec Security Scanner
        run: |
          gosec -fmt sarif -out gosec-results.sarif ./...

      - name: Upload Gosec results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: gosec-results.sarif

      - name: Install Nancy vulnerability scanner
        run: |
          go install github.com/sonatypecommunity/nancy@latest

      - name: Run Nancy vulnerability scanner
        run: |
          cd web3-wallet-backend
          go list -json -deps ./... | nancy sleuth

  test-summary:
    name: Test Summary
    runs-on: ubuntu-latest
    needs: [unit-tests, integration-tests, security-tests]
    if: always()

    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4

      - name: Generate test summary
        run: |
          echo "# Test Results Summary" > test_summary.md
          echo "" >> test_summary.md
          echo "## Unit Tests" >> test_summary.md
          echo "Status: ${{ needs.unit-tests.result }}" >> test_summary.md
          echo "" >> test_summary.md
          echo "## Integration Tests" >> test_summary.md
          echo "Status: ${{ needs.integration-tests.result }}" >> test_summary.md
          echo "" >> test_summary.md
          echo "## Security Tests" >> test_summary.md
          echo "Status: ${{ needs.security-tests.result }}" >> test_summary.md
          echo "" >> test_summary.md

          if [ "${{ needs.unit-tests.result }}" == "success" ] && \
             [ "${{ needs.integration-tests.result }}" == "success" ] && \
             [ "${{ needs.security-tests.result }}" == "success" ]; then
            echo "✅ All tests passed!" >> test_summary.md
          else
            echo "❌ Some tests failed. Please check the individual job results." >> test_summary.md
          fi

      - name: Upload test summary
        uses: actions/upload-artifact@v4
        with:
          name: test-summary
          path: test_summary.md
