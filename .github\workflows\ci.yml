name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  GO_VERSION: '1.21'
  DOCKER_REGISTRY: ghcr.io
  IMAGE_NAME: fintech-platform

jobs:
  # Code Quality and Security
  lint-and-security:
    name: Lint and <PERSON> Scan
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run golangci-lint
      uses: golangci/golangci-lint-action@v3
      with:
        version: latest
        args: --timeout=5m

    - name: Install gosec
      run: |
        go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest

    - name: Run gosec security scanner
      run: |
        cd web3-wallet-backend
        gosec -fmt sarif -out gosec.sarif ./...

    - name: Upload SARIF file
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: web3-wallet-backend/gosec.sarif
      if: always()

    - name: Install nancy
      run: |
        go install github.com/sonatypecommunity/nancy@latest

    - name: Run Nancy vulnerability scanner
      run: |
        cd web3-wallet-backend
        go list -json -deps ./... | nancy sleuth

  # Unit Tests
  unit-tests:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Run unit tests
      run: |
        cd web3-wallet-backend
        go test -v -race -coverprofile=coverage.out ./...
        go tool cover -html=coverage.out -o coverage.html

    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./web3-wallet-backend/coverage.out
        flags: unittests
        name: codecov-umbrella

    - name: Upload coverage artifacts
      uses: actions/upload-artifact@v3
      with:
        name: coverage-report
        path: web3-wallet-backend/coverage.html

  # Integration Tests
  integration-tests:
    name: Integration Tests
    runs-on: ubuntu-latest
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_fintech
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Cache Go modules
      uses: actions/cache@v3
      with:
        path: ~/go/pkg/mod
        key: ${{ runner.os }}-go-${{ hashFiles('**/go.sum') }}
        restore-keys: |
          ${{ runner.os }}-go-

    - name: Install dependencies
      run: go mod download

    - name: Wait for services
      run: |
        timeout 30 bash -c 'until pg_isready -h localhost -p 5432; do sleep 1; done'
        timeout 30 bash -c 'until redis-cli -h localhost -p 6379 ping; do sleep 1; done'

    - name: Run integration tests
      env:
        INTEGRATION_TESTS: 1
        DATABASE_HOST: localhost
        DATABASE_PORT: 5432
        DATABASE_NAME: test_fintech
        DATABASE_USER: postgres
        DATABASE_PASSWORD: postgres
        REDIS_HOST: localhost
        REDIS_PORT: 6379
      run: |
        cd web3-wallet-backend
        go test -v -tags=integration ./...

  # Build and Test Docker Images
  docker-build:
    name: Docker Build and Test
    runs-on: ubuntu-latest
    needs: [lint-and-security, unit-tests]
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build fintech-api image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./web3-wallet-backend/Dockerfile.fintech
        target: fintech-api
        push: false
        tags: fintech-api:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: Test Docker image
      run: |
        docker run --rm fintech-api:test --version || true

    - name: Run Docker Compose tests
      run: |
        cp .env.fintech.example .env
        docker-compose -f docker-compose.fintech.yml up -d postgres redis
        sleep 30
        docker-compose -f docker-compose.fintech.yml run --rm fintech-api go test -v ./...
        docker-compose -f docker-compose.fintech.yml down

  # Performance Tests
  performance-tests:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Go
      uses: actions/setup-go@v4
      with:
        go-version: ${{ env.GO_VERSION }}

    - name: Install k6
      run: |
        sudo gpg -k
        sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
        echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
        sudo apt-get update
        sudo apt-get install k6

    - name: Start services
      run: |
        cp .env.fintech.example .env
        docker-compose -f docker-compose.fintech.yml up -d
        sleep 60

    - name: Run performance tests
      run: |
        k6 run --out json=performance-results.json tests/performance/load-test.js

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results.json

    - name: Stop services
      run: docker-compose -f docker-compose.fintech.yml down

  # Build and Push Images (only on main branch)
  build-and-push:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: [integration-tests, docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    permissions:
      contents: read
      packages: write
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Log in to Container Registry
      uses: docker/login-action@v3
      with:
        registry: ${{ env.DOCKER_REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Extract metadata
      id: meta
      uses: docker/metadata-action@v5
      with:
        images: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}
        tags: |
          type=ref,event=branch
          type=ref,event=pr
          type=sha,prefix={{branch}}-
          type=raw,value=latest,enable={{is_default_branch}}

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build and push fintech-api
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./web3-wallet-backend/Dockerfile.fintech
        target: fintech-api
        push: true
        tags: ${{ steps.meta.outputs.tags }}
        labels: ${{ steps.meta.outputs.labels }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Deploy to staging
      run: |
        echo "Deploying to staging environment..."
        # Add actual deployment commands here
        # kubectl apply -f k8s/staging/
        # helm upgrade --install fintech-staging ./helm-chart

  # Security Scan of Docker Images
  security-scan:
    name: Security Scan Docker Images
    runs-on: ubuntu-latest
    needs: [build-and-push]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: ${{ env.DOCKER_REGISTRY }}/${{ github.repository }}/${{ env.IMAGE_NAME }}:latest
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

  # Notify on Success/Failure
  notify:
    name: Notify
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: always()
    steps:
    - name: Notify Slack on success
      if: success() && env.SLACK_WEBHOOK_URL != ''
      uses: 8398a7/action-slack@v3
      with:
        status: success
        text: "✅ Fintech Platform CI/CD pipeline completed successfully!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

    - name: Notify Slack on failure
      if: failure() && env.SLACK_WEBHOOK_URL != ''
      uses: 8398a7/action-slack@v3
      with:
        status: failure
        text: "❌ Fintech Platform CI/CD pipeline failed!"
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
