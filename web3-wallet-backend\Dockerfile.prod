# Multi-stage build for production
# Stage 1: Build stage
FROM golang:1.21-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o main ./cmd/server

# Stage 2: Final stage
FROM alpine:3.18

# Install runtime dependencies
RUN apk --no-cache add ca-certificates curl tzdata && \
    addgroup -g 1001 -S appgroup && \
    adduser -u 1001 -S appuser -G appgroup

# Set timezone
ENV TZ=UTC

# Create necessary directories
RUN mkdir -p /app/configs /app/logs /app/data && \
    chown -R appuser:appgroup /app

# Copy binary from builder stage
COPY --from=builder /app/main /app/main

# Copy configuration files
COPY --chown=appuser:appgroup configs/ /app/configs/

# Copy migration files if they exist
COPY --chown=appuser:appgroup migrations/ /app/migrations/

# Set working directory
WORKDIR /app

# Switch to non-root user
USER appuser

# Expose ports
EXPOSE 8080 9090 8081

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8081/health || exit 1

# Set environment variables
ENV ENV=production
ENV CONFIG_PATH=/app/configs/production.yaml
ENV LOG_LEVEL=info
ENV GIN_MODE=release

# Run the application
CMD ["./main"]
