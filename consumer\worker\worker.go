package worker

import (
	"encoding/json"
	"log"
	"sync"
	"time"

	"github.com/IBM/sarama"
)

// Order struct
type Order struct {
	CustomerName string `json:"customer_name"`
	CoffeeType   string `json:"coffee_type"`
}

// ProcessedOrder represents a processed coffee order
type ProcessedOrder struct {
	OrderID         string    `json:"order_id"`
	CustomerName    string    `json:"customer_name"`
	CoffeeType      string    `json:"coffee_type"`
	Status          string    `json:"status"`
	ProcessedAt     time.Time `json:"processed_at"`
	PreparationTime int       `json:"preparation_time"` // in seconds
}

// Worker represents a worker that processes messages
type Worker struct {
	id         int
	jobQueue   <-chan *sarama.ConsumerMessage
	quit       chan bool
	wg         *sync.WaitGroup
	workerPool int
}

// NewWorker creates a new worker
func NewWorker(id int, jobQueue <-chan *sarama.ConsumerMessage, wg *sync.WaitGroup, workerPool int) *Worker {
	return &Worker{
		id:         id,
		jobQueue:   jobQueue,
		quit:       make(chan bool),
		wg:         wg,
		workerPool: workerPool,
	}
}

// Start starts the worker
func (w *Worker) Start() {
	go func() {
		defer w.wg.Done()
		for {
			select {
			case msg := <-w.jobQueue:
				w.processMessage(msg)
			case <-w.quit:
				log.Printf("Worker %d stopping", w.id)
				return
			}
		}
	}()
}

// Stop stops the worker
func (w *Worker) Stop() {
	go func() {
		w.quit <- true
	}()
}

// processMessage processes a message
func (w *Worker) processMessage(msg *sarama.ConsumerMessage) {
	log.Printf("Worker %d processing message from topic %s, partition %d, offset %d",
		w.id, msg.Topic, msg.Partition, msg.Offset)

	switch msg.Topic {
	case "coffee_orders":
		w.processOrder(msg)
	case "processed_orders":
		w.processProcessedOrder(msg)
	default:
		log.Printf("Unknown topic: %s", msg.Topic)
	}
}

// processOrder processes an order message
func (w *Worker) processOrder(msg *sarama.ConsumerMessage) {
	var order Order
	if err := json.Unmarshal(msg.Value, &order); err != nil {
		log.Printf("Error parsing order: %v", err)
		return
	}

	log.Printf("Worker %d received order for %s coffee from %s",
		w.id, order.CoffeeType, order.CustomerName)
}

// processProcessedOrder processes a processed order message
func (w *Worker) processProcessedOrder(msg *sarama.ConsumerMessage) {
	var processedOrder ProcessedOrder
	if err := json.Unmarshal(msg.Value, &processedOrder); err != nil {
		log.Printf("Error parsing processed order: %v", err)
		return
	}

	log.Printf("Worker %d brewing %s coffee for %s (Order ID: %s, Preparation Time: %d seconds)",
		w.id, processedOrder.CoffeeType, processedOrder.CustomerName, processedOrder.OrderID, processedOrder.PreparationTime)

	// Simulate coffee preparation
	log.Printf("Worker %d starting preparation of %s coffee for %s...",
		w.id, processedOrder.CoffeeType, processedOrder.CustomerName)
	time.Sleep(time.Duration(processedOrder.PreparationTime) * time.Millisecond) // Use milliseconds instead of seconds for demo
	log.Printf("Worker %d finished preparing %s coffee for %s!",
		w.id, processedOrder.CoffeeType, processedOrder.CustomerName)
}

// WorkerPool represents a pool of workers
type WorkerPool struct {
	jobQueue   chan *sarama.ConsumerMessage
	workers    []*Worker
	workerPool int
	wg         sync.WaitGroup
}

// NewWorkerPool creates a new worker pool
func NewWorkerPool(workerPool int) *WorkerPool {
	return &WorkerPool{
		jobQueue:   make(chan *sarama.ConsumerMessage, 100),
		workerPool: workerPool,
	}
}

// Start starts the worker pool
func (wp *WorkerPool) Start() {
	// Create and start workers
	wp.workers = make([]*Worker, wp.workerPool)
	for i := 0; i < wp.workerPool; i++ {
		wp.wg.Add(1)
		wp.workers[i] = NewWorker(i, wp.jobQueue, &wp.wg, wp.workerPool)
		wp.workers[i].Start()
		log.Printf("Worker %d started", i)
	}
}

// Stop stops the worker pool
func (wp *WorkerPool) Stop() {
	// Stop workers
	for i := 0; i < wp.workerPool; i++ {
		wp.workers[i].Stop()
	}
	wp.wg.Wait()
	close(wp.jobQueue)
}

// Submit submits a message to the worker pool
func (wp *WorkerPool) Submit(msg *sarama.ConsumerMessage) {
	wp.jobQueue <- msg
}
