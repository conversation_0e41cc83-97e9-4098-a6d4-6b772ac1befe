# Nginx Configuration for Fintech Platform
# This configuration provides load balancing, SSL termination, and security features

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Load dynamic modules
load_module modules/ngx_http_geoip_module.so;

events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging Configuration
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for"';

    log_format fintech_access '$remote_addr - $remote_user [$time_local] '
                              '"$request" $status $body_bytes_sent '
                              '"$http_referer" "$http_user_agent" '
                              '$request_time $upstream_response_time '
                              '$upstream_addr $upstream_status';

    access_log /var/log/nginx/access.log fintech_access;

    # Basic Settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    server_tokens off;

    # Buffer Settings
    client_body_buffer_size 128k;
    client_max_body_size 10m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;

    # Timeout Settings
    client_body_timeout 12;
    client_header_timeout 12;
    keepalive_timeout 15;
    send_timeout 10;

    # Gzip Settings
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' wss: https:; frame-ancestors 'none';";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()";

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=register:10m rate=2r/m;

    # Connection Limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

    # Upstream Configuration
    upstream fintech_api {
        least_conn;
        server fintech-api:8080 max_fails=3 fail_timeout=30s;
        # Add more backend servers for load balancing
        # server fintech-api-2:8080 max_fails=3 fail_timeout=30s;
        # server fintech-api-3:8080 max_fails=3 fail_timeout=30s;
        keepalive 32;
    }

    upstream content_analysis {
        least_conn;
        server content-analysis-service:8085 max_fails=3 fail_timeout=30s;
        keepalive 16;
    }

    upstream grafana {
        server grafana:3000;
    }

    upstream prometheus {
        server prometheus:9090;
    }

    # Main Server Configuration
    server {
        listen 80;
        server_name localhost fintech.local api.fintech.local;
        
        # Redirect HTTP to HTTPS in production
        # return 301 https://$server_name$request_uri;

        # Security
        limit_conn conn_limit_per_ip 20;

        # Health Check Endpoint
        location /health {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # API Routes
        location /api/ {
            # Rate limiting
            limit_req zone=api burst=20 nodelay;
            
            # CORS Headers
            add_header Access-Control-Allow-Origin "*" always;
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS" always;
            add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization" always;
            add_header Access-Control-Expose-Headers "Content-Length,Content-Range" always;

            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "*";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type "text/plain; charset=utf-8";
                add_header Content-Length 0;
                return 204;
            }

            # Proxy to backend
            proxy_pass http://fintech_api;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_cache_bypass $http_upgrade;
            proxy_read_timeout 300s;
            proxy_connect_timeout 75s;
        }

        # Authentication Endpoints (stricter rate limiting)
        location ~ ^/api/v1/accounts/(login|register) {
            limit_req zone=login burst=5 nodelay;
            
            proxy_pass http://fintech_api;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Content Analysis Service
        location /content-analysis/ {
            limit_req zone=api burst=10 nodelay;
            
            proxy_pass http://content_analysis/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # Static Files
        location /static/ {
            alias /usr/share/nginx/html/static/;
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options nosniff;
        }

        # Documentation
        location /docs/ {
            alias /usr/share/nginx/html/docs/;
            index index.html;
            try_files $uri $uri/ /docs/index.html;
        }

        # Monitoring (restrict access in production)
        location /grafana/ {
            proxy_pass http://grafana/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Basic auth for production
            # auth_basic "Monitoring";
            # auth_basic_user_file /etc/nginx/.htpasswd;
        }

        location /prometheus/ {
            proxy_pass http://prometheus/;
            proxy_http_version 1.1;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # Basic auth for production
            # auth_basic "Monitoring";
            # auth_basic_user_file /etc/nginx/.htpasswd;
        }

        # Deny access to sensitive files
        location ~ /\. {
            deny all;
            access_log off;
            log_not_found off;
        }

        location ~ \.(env|config|ini|log|sh|sql)$ {
            deny all;
            access_log off;
            log_not_found off;
        }

        # Default location
        location / {
            root /usr/share/nginx/html;
            index index.html index.htm;
            try_files $uri $uri/ /index.html;
        }

        # Error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        location = /50x.html {
            root /usr/share/nginx/html;
        }
    }

    # HTTPS Server Configuration (for production)
    # server {
    #     listen 443 ssl http2;
    #     server_name fintech.local api.fintech.local;
    #
    #     # SSL Configuration
    #     ssl_certificate /etc/nginx/ssl/fintech.crt;
    #     ssl_certificate_key /etc/nginx/ssl/fintech.key;
    #     ssl_protocols TLSv1.2 TLSv1.3;
    #     ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    #     ssl_prefer_server_ciphers off;
    #     ssl_session_cache shared:SSL:10m;
    #     ssl_session_timeout 10m;
    #     ssl_stapling on;
    #     ssl_stapling_verify on;
    #
    #     # Include the same location blocks as HTTP server
    #     include /etc/nginx/conf.d/fintech-locations.conf;
    # }

    # WebSocket Support (for real-time features)
    map $http_upgrade $connection_upgrade {
        default upgrade;
        '' close;
    }

    # Logging for debugging
    error_log /var/log/nginx/error.log debug;
}

# Stream Configuration (for TCP/UDP load balancing if needed)
# stream {
#     upstream database {
#         server postgres:5432;
#     }
#
#     server {
#         listen 5432;
#         proxy_pass database;
#         proxy_timeout 1s;
#         proxy_responses 1;
#     }
# }
