apiVersion: apps/v1
kind: Deployment
metadata:
  name: producer
  namespace: coffee-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: producer
  template:
    metadata:
      labels:
        app: producer
    spec:
      containers:
      - name: producer
        image: ${DOCKER_REGISTRY}/coffee-producer:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 3000
        env:
        - name: SERVER_PORT
          value: "3000"
        - name: KAFKA_BROKERS
          valueFrom:
            configMapKeyRef:
              name: coffee-config
              key: KAFKA_BROKERS
        - name: KAFKA_TOPIC
          valueFrom:
            configMapKeyRef:
              name: coffee-config
              key: KAFKA_TOPIC
        - name: KAFKA_RETRY_MAX
          valueFrom:
            configMapKeyRef:
              name: coffee-config
              key: KAFKA_RETRY_MAX
        - name: KAFKA_REQUIRED_ACKS
          valueFrom:
            configMapKeyRef:
              name: coffee-config
              key: KAFKA_REQUIRED_ACKS
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "500m"
        readinessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 10
          periodSeconds: 5
        livenessProbe:
          httpGet:
            path: /health
            port: 3000
          initialDelaySeconds: 20
          periodSeconds: 10
---
apiVersion: v1
kind: Service
metadata:
  name: producer-service
  namespace: coffee-system
spec:
  selector:
    app: producer
  ports:
  - port: 80
    targetPort: 3000
  type: ClusterIP
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: producer-ingress
  namespace: coffee-system
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
spec:
  rules:
  - host: coffee-api.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: producer-service
            port:
              number: 80
