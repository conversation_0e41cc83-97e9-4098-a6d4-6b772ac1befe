name: Deploy Tel<PERSON><PERSON> <PERSON><PERSON>

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production
      telegram_token:
        description: 'Telegram <PERSON><PERSON> Token (optional - uses secret if not provided)'
        required: false
        type: string
      gemini_api_key:
        description: 'Gemini API Key (optional - uses secret if not provided)'
        required: false
        type: string

env:
  REGISTRY: ghcr.io
  REGISTRY_USERNAME: ${{ github.actor }}
  REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
  REGISTRY_NAMESPACE: ${{ github.repository_owner }}
  IMAGE_NAME: web3-coffee-telegram-bot

jobs:
  deploy-telegram-bot:
    runs-on: ubuntu-latest
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.REGISTRY_USERNAME }}
          password: ${{ env.REGISTRY_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAMESPACE }}/${{ env.IMAGE_NAME }}
          tags: |
            type=sha,format=short
            type=ref,event=branch
            type=raw,value=${{ github.event.inputs.environment }},enable=true
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push Telegram bot image
        uses: docker/build-push-action@v5
        with:
          context: ./web3-wallet-backend
          file: ./web3-wallet-backend/deployments/telegram-bot/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=telegram-bot
          cache-to: type=gha,mode=max,scope=telegram-bot

      - name: Deploy to staging
        if: github.event.inputs.environment == 'staging'
        run: |
          echo "Deploying to staging environment..."

          # Set environment variables
          export TELEGRAM_BOT_TOKEN="${{ github.event.inputs.telegram_token || secrets.TELEGRAM_BOT_TOKEN_STAGING }}"
          export GEMINI_API_KEY="${{ github.event.inputs.gemini_api_key || secrets.GEMINI_API_KEY_STAGING }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Create deployment configuration
          cd web3-wallet-backend/deployments/telegram-bot

          # Update docker-compose for staging
          cat > docker-compose.staging.yml <<EOF
          version: '3.8'
          services:
            telegram-bot:
              image: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAMESPACE }}/${{ env.IMAGE_NAME }}:${IMAGE_TAG}
              environment:
                - TELEGRAM_BOT_TOKEN=${TELEGRAM_BOT_TOKEN}
                - GEMINI_API_KEY=${GEMINI_API_KEY}
                - APP_ENV=staging
                - LOG_LEVEL=debug
                - REDIS_HOST=redis
                - REDIS_PORT=6379
              depends_on:
                - redis
                - postgres
              networks:
                - web3-coffee-network

            redis:
              image: redis:7-alpine
              ports:
                - "6379:6379"
              networks:
                - web3-coffee-network

            postgres:
              image: postgres:15-alpine
              environment:
                - POSTGRES_DB=web3_coffee_staging
                - POSTGRES_USER=web3_user
                - POSTGRES_PASSWORD=web3_password
              ports:
                - "5432:5432"
              networks:
                - web3-coffee-network

          networks:
            web3-coffee-network:
              driver: bridge
          EOF

          echo "Staging deployment configuration created"

      - name: Deploy to production
        if: github.event.inputs.environment == 'production'
        run: |
          echo "Deploying to production environment..."

          # Set environment variables
          export TELEGRAM_BOT_TOKEN="${{ github.event.inputs.telegram_token || secrets.TELEGRAM_BOT_TOKEN_PRODUCTION }}"
          export GEMINI_API_KEY="${{ github.event.inputs.gemini_api_key || secrets.GEMINI_API_KEY_PRODUCTION }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Create Kubernetes deployment
          cat > telegram-bot-deployment.yaml <<EOF
          apiVersion: apps/v1
          kind: Deployment
          metadata:
            name: telegram-bot
            namespace: coffee-system
            labels:
              app: telegram-bot
              version: ${IMAGE_TAG}
          spec:
            replicas: 2
            selector:
              matchLabels:
                app: telegram-bot
            template:
              metadata:
                labels:
                  app: telegram-bot
                  version: ${IMAGE_TAG}
              spec:
                containers:
                - name: telegram-bot
                  image: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAMESPACE }}/${{ env.IMAGE_NAME }}:${IMAGE_TAG}
                  env:
                  - name: TELEGRAM_BOT_TOKEN
                    valueFrom:
                      secretKeyRef:
                        name: telegram-bot-secrets
                        key: bot-token
                  - name: GEMINI_API_KEY
                    valueFrom:
                      secretKeyRef:
                        name: telegram-bot-secrets
                        key: gemini-api-key
                  - name: APP_ENV
                    value: "production"
                  - name: LOG_LEVEL
                    value: "info"
                  - name: REDIS_HOST
                    value: "redis-service"
                  - name: REDIS_PORT
                    value: "6379"
                  ports:
                  - containerPort: 8087
                    name: http
                  resources:
                    requests:
                      memory: "256Mi"
                      cpu: "250m"
                    limits:
                      memory: "512Mi"
                      cpu: "500m"
                  livenessProbe:
                    httpGet:
                      path: /health
                      port: 8087
                    initialDelaySeconds: 30
                    periodSeconds: 10
                  readinessProbe:
                    httpGet:
                      path: /ready
                      port: 8087
                    initialDelaySeconds: 5
                    periodSeconds: 5
          ---
          apiVersion: v1
          kind: Service
          metadata:
            name: telegram-bot-service
            namespace: coffee-system
          spec:
            selector:
              app: telegram-bot
            ports:
            - port: 80
              targetPort: 8087
              name: http
          EOF

          echo "Production Kubernetes manifests created"

      - name: Notify deployment status
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Telegram bot deployed successfully to ${{ github.event.inputs.environment }}"
            echo "Image: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAMESPACE }}/${{ env.IMAGE_NAME }}:sha-$(git rev-parse --short HEAD)"
          else
            echo "❌ Telegram bot deployment failed"
          fi

      - name: Upload deployment artifacts
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: telegram-bot-deployment-${{ github.event.inputs.environment }}
          path: |
            web3-wallet-backend/deployments/telegram-bot/docker-compose.staging.yml
            telegram-bot-deployment.yaml
          retention-days: 30
