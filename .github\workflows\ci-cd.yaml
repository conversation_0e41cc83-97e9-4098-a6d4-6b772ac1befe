name: CI/CD Pipeline

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

env:
  REGISTRY: ghcr.io
  REGISTRY_USERNAME: ${{ github.actor }}
  REGISTRY_PASSWORD: ${{ secrets.GITHUB_TOKEN }}
  REGISTRY_NAMESPACE: ${{ github.repository_owner }}
  KUBERNETES_NAMESPACE: coffee-system

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      # Legacy services
      - name: Build Producer
        run: |
          cd producer
          go mod tidy
          go build -v ./...

      - name: Test Producer
        run: |
          cd producer
          go test -v ./...

      - name: Build Consumer
        run: |
          cd consumer
          go mod tidy
          go build -v ./...

      - name: Test Consumer
        run: |
          cd consumer
          go test -v ./...

      - name: Build Streams
        run: |
          cd streams
          go mod tidy
          go build -v ./...

      - name: Test Streams
        run: |
          cd streams
          go test -v ./...

      # Web3 Wallet Backend Services
      - name: Build Web3 Wallet Backend
        run: |
          cd web3-wallet-backend
          go mod tidy
          go build -v ./cmd/...

      - name: Test Web3 Wallet Backend
        run: |
          cd web3-wallet-backend
          go test -v ./internal/... ./pkg/...

      # Accounts Service
      - name: Build Accounts Service
        run: |
          cd accounts-service
          go mod tidy
          go build -v ./...

      - name: Test Accounts Service
        run: |
          cd accounts-service
          go test -v ./...

      # AI Agents
      - name: Build AI Agents
        run: |
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ]; then
              echo "Building ${agent_dir%/}"
              cd "$agent_dir"
              go mod tidy || echo "No go.mod in ${agent_dir%/}, skipping mod tidy"
              go build -v ./...
              cd ..
            fi
          done

      - name: Test AI Agents
        run: |
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ]; then
              echo "Testing ${agent_dir%/}"
              cd "$agent_dir"
              go test -v ./... || echo "No tests in ${agent_dir%/}"
              cd ..
            fi
          done

  build-and-push-images:
    needs: build-and-test
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    strategy:
      matrix:
        service:
          # Legacy services
          - name: producer
            context: ./producer
            image: coffee-producer
          - name: consumer
            context: ./consumer
            image: coffee-consumer
          - name: streams
            context: ./streams
            image: coffee-streams
          # Web3 Wallet Backend services
          - name: telegram-bot
            context: ./web3-wallet-backend
            dockerfile: ./web3-wallet-backend/deployments/telegram-bot/Dockerfile
            image: web3-coffee-telegram-bot
          - name: wallet-service
            context: ./web3-wallet-backend
            dockerfile: ./web3-wallet-backend/build/wallet-service/Dockerfile
            image: web3-coffee-wallet-service
          - name: api-gateway
            context: ./web3-wallet-backend
            dockerfile: ./web3-wallet-backend/build/api-gateway/Dockerfile
            image: web3-coffee-api-gateway
          - name: defi-service
            context: ./web3-wallet-backend
            dockerfile: ./web3-wallet-backend/build/defi-service/Dockerfile
            image: web3-coffee-defi-service
          # Accounts service
          - name: accounts-service
            context: ./accounts-service
            image: coffee-accounts-service
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to GitHub Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ env.REGISTRY_USERNAME }}
          password: ${{ env.REGISTRY_PASSWORD }}

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.REGISTRY_NAMESPACE }}/${{ matrix.service.image }}
          tags: |
            type=sha,format=short
            type=ref,event=branch
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Build and push image
        uses: docker/build-push-action@v5
        with:
          context: ${{ matrix.service.context }}
          file: ${{ matrix.service.dockerfile || format('{0}/Dockerfile', matrix.service.context) }}
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha,scope=${{ matrix.service.name }}
          cache-to: type=gha,mode=max,scope=${{ matrix.service.name }}

  deploy:
    needs: build-and-push-images
    runs-on: ubuntu-latest
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Helm
        uses: azure/setup-helm@v4
        with:
          version: 'latest'

      - name: Set up kubectl
        uses: azure/setup-kubectl@v4
        with:
          version: 'latest'

      - name: Configure Kubernetes
        uses: azure/k8s-set-context@v4
        with:
          method: kubeconfig
          kubeconfig: ${{ secrets.KUBECONFIG }}

      - name: Deploy Legacy Services
        run: |
          export REGISTRY="${{ env.REGISTRY }}"
          export REGISTRY_NAMESPACE="${{ env.REGISTRY_NAMESPACE }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Update values file with image tag for legacy services
          cat > kubernetes/helm/coffee-system/values-prod.yaml <<EOF
          global:
            registry: ${REGISTRY}/${REGISTRY_NAMESPACE}/
            imagePullPolicy: Always
            environment: production

          producer:
            image: coffee-producer
            tag: ${IMAGE_TAG}

          consumer:
            image: coffee-consumer
            tag: ${IMAGE_TAG}

          streams:
            image: coffee-streams
            tag: ${IMAGE_TAG}
          EOF

          # Deploy legacy services with Helm
          helm upgrade --install coffee-system ./kubernetes/helm/coffee-system \
            --namespace ${{ env.KUBERNETES_NAMESPACE }} \
            --create-namespace \
            -f kubernetes/helm/coffee-system/values-prod.yaml

      - name: Deploy Web3 Wallet Backend
        run: |
          export REGISTRY="${{ env.REGISTRY }}"
          export REGISTRY_NAMESPACE="${{ env.REGISTRY_NAMESPACE }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Deploy Web3 services using kubectl
          cd web3-wallet-backend/kubernetes/manifests

          # Replace image tags in manifests
          find . -name "*.yaml" -exec sed -i "s|{{IMAGE_TAG}}|${IMAGE_TAG}|g" {} \;
          find . -name "*.yaml" -exec sed -i "s|{{REGISTRY}}|${REGISTRY}/${REGISTRY_NAMESPACE}|g" {} \;

          # Apply manifests
          kubectl apply -f . --namespace ${{ env.KUBERNETES_NAMESPACE }}

      - name: Deploy Telegram Bot
        run: |
          export REGISTRY="${{ env.REGISTRY }}"
          export REGISTRY_NAMESPACE="${{ env.REGISTRY_NAMESPACE }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Deploy Telegram bot using Docker Compose for development
          # In production, this would be deployed to Kubernetes
          cd web3-wallet-backend/deployments/telegram-bot

          # Update docker-compose with production image
          sed -i "s|build: ../../|image: ${REGISTRY}/${REGISTRY_NAMESPACE}/web3-coffee-telegram-bot:${IMAGE_TAG}|g" docker-compose.yml

          echo "Telegram bot deployment configuration updated"

      - name: Deploy Accounts Service
        run: |
          export REGISTRY="${{ env.REGISTRY }}"
          export REGISTRY_NAMESPACE="${{ env.REGISTRY_NAMESPACE }}"
          export IMAGE_TAG="sha-$(git rev-parse --short HEAD)"

          # Deploy accounts service
          cd accounts-service
          kubectl apply -f kubernetes/ --namespace ${{ env.KUBERNETES_NAMESPACE }}

          # Update deployment with new image
          kubectl set image deployment/accounts-service \
            accounts-service=${REGISTRY}/${REGISTRY_NAMESPACE}/coffee-accounts-service:${IMAGE_TAG} \
            --namespace ${{ env.KUBERNETES_NAMESPACE }}

  security-scan:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@0.16.1
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'trivy-results.sarif'

      - name: Upload Trivy scan results to GitHub Security tab
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: 'trivy-results.sarif'

      - name: Install Gosec
        run: |
          curl -sfL https://raw.githubusercontent.com/securecodewarrior/gosec/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.18.2
          echo "$(go env GOPATH)/bin" >> $GITHUB_PATH

      - name: Run Gosec Security Scanner
        run: |
          gosec -fmt sarif -out gosec-results.sarif ./...

      - name: Upload Gosec results
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: gosec-results.sarif

  code-quality:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: Run golangci-lint for Web3 Wallet Backend
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: web3-wallet-backend

      - name: Run golangci-lint for Producer
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: producer

      - name: Run golangci-lint for Consumer
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: consumer

      - name: Run golangci-lint for Streams
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: streams

      - name: Run golangci-lint for Accounts Service
        uses: golangci/golangci-lint-action@v4
        with:
          version: latest
          working-directory: accounts-service

      - name: Run golangci-lint for AI Agents
        run: |
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ]; then
              echo "Running golangci-lint for ${agent_dir%/}"
              cd "$agent_dir"
              if [ -f "go.mod" ]; then
                golangci-lint run --timeout=5m || echo "Linting issues in ${agent_dir%/}"
              else
                echo "No go.mod in ${agent_dir%/}, skipping golangci-lint"
              fi
              cd ..
            fi
          done

      - name: Run gofmt check for all services
        run: |
          for dir in web3-wallet-backend producer consumer streams accounts-service; do
            if [ -d "$dir" ]; then
              echo "Checking gofmt for $dir"
              cd "$dir"
              if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
                echo "The following files in $dir are not formatted:"
                gofmt -s -l .
                exit 1
              fi
              cd ..
            fi
          done

          # Check AI agents
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ]; then
              echo "Checking gofmt for ai-agents/${agent_dir%/}"
              cd "$agent_dir"
              if [ "$(gofmt -s -l . | wc -l)" -gt 0 ]; then
                echo "The following files in ai-agents/${agent_dir%/} are not formatted:"
                gofmt -s -l .
                exit 1
              fi
              cd ..
            fi
          done

      - name: Run go vet for all services
        run: |
          for dir in web3-wallet-backend producer consumer streams accounts-service; do
            if [ -d "$dir" ]; then
              echo "Running go vet for $dir"
              cd "$dir"
              go vet ./...
              cd ..
            fi
          done

          # Check AI agents
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ]; then
              echo "Running go vet for ai-agents/${agent_dir%/}"
              cd "$agent_dir"
              go vet ./... || echo "go vet issues in ai-agents/${agent_dir%/}"
              cd ..
            fi
          done

      - name: Check go mod tidy for all services
        run: |
          for dir in web3-wallet-backend producer consumer streams accounts-service; do
            if [ -d "$dir" ]; then
              echo "Checking go mod tidy for $dir"
              cd "$dir"
              go mod tidy
              if ! git diff --exit-code go.mod go.sum; then
                echo "go.mod or go.sum is not tidy in $dir"
                exit 1
              fi
              cd ..
            fi
          done

          # Check AI agents
          cd ai-agents
          for agent_dir in */; do
            if [ -f "${agent_dir}main.go" ] && [ -f "${agent_dir}go.mod" ]; then
              echo "Checking go mod tidy for ai-agents/${agent_dir%/}"
              cd "$agent_dir"
              go mod tidy
              if ! git diff --exit-code go.mod go.sum; then
                echo "go.mod or go.sum is not tidy in ai-agents/${agent_dir%/}"
                exit 1
              fi
              cd ..
            fi
          done

  integration-tests:
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request'
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      postgres:
        image: postgres:15-alpine
        env:
          POSTGRES_DB: test_db
          POSTGRES_USER: test_user
          POSTGRES_PASSWORD: test_password
        ports:
          - 5432:5432
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.22'

      - name: Run integration tests
        env:
          REDIS_HOST: localhost
          REDIS_PORT: 6379
          DB_HOST: localhost
          DB_PORT: 5432
          DB_NAME: test_db
          DB_USER: test_user
          DB_PASSWORD: test_password
        run: |
          cd web3-wallet-backend
          go test -v -tags=integration ./tests/integration/...
