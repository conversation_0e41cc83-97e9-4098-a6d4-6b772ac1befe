apiVersion: v1
kind: Secret
metadata:
  name: fintech-secrets
  namespace: fintech-platform
type: Opaque
data:
  # Base64 encoded secrets - replace with actual values
  # echo -n "your-secret" | base64
  
  # Database
  DATABASE_PASSWORD: cG9zdGdyZXM=  # postgres
  
  # Security
  JWT_SECRET: eW91ci1qd3Qtc2VjcmV0LWtleS1jaGFuZ2UtdGhpcy1pbi1wcm9kdWN0aW9u  # your-jwt-secret-key-change-this-in-production
  WEBHOOK_SECRET: eW91ci13ZWJob29rLXNlY3JldC1rZXktY2hhbmdlLXRoaXM=  # your-webhook-secret-key-change-this
  ENCRYPTION_KEY: eW91ci0zMi1jaGFyYWN0ZXItZW5jcnlwdGlvbi1rZXk=  # your-32-character-encryption-key
  
  # Email (SMTP)
  SMTP_USERNAME: ****************************  # <EMAIL>
  SMTP_PASSWORD: eW91ci1hcHAtcGFzc3dvcmQ=  # your-app-password
  
  # SMS (Twilio)
  SMS_API_KEY: eW91ci10d2lsaW8tYWNjb3VudC1zaWQ=  # your-twilio-account-sid
  SMS_API_SECRET: eW91ci10d2lsaW8tYXV0aC10b2tlbg==  # your-twilio-auth-token
  
  # Push Notifications (Firebase)
  PUSH_API_KEY: eW91ci1maXJlYmFzZS1zZXJ2ZXIta2V5  # your-firebase-server-key
  
  # Blockchain
  ETHEREUM_PRIVATE_KEY: eW91ci1ldGhlcmV1bS1wcml2YXRlLWtleQ==  # your-ethereum-private-key
  BITCOIN_RPC_USERNAME: eW91ci1iaXRjb2luLXJwYy11c2VybmFtZQ==  # your-bitcoin-rpc-username
  BITCOIN_RPC_PASSWORD: eW91ci1iaXRjb2luLXJwYy1wYXNzd29yZA==  # your-bitcoin-rpc-password
  SOLANA_PRIVATE_KEY: eW91ci1zb2xhbmEtcHJpdmF0ZS1rZXk=  # your-solana-private-key
  
  # KYC Providers
  JUMIO_API_TOKEN: eW91ci1qdW1pby1hcGktdG9rZW4=  # your-jumio-api-token
  JUMIO_API_SECRET: eW91ci1qdW1pby1hcGktc2VjcmV0  # your-jumio-api-secret
  ONFIDO_API_TOKEN: eW91ci1vbmZpZG8tYXBpLXRva2Vu  # your-onfido-api-token
  
  # Payment Processors
  STRIPE_SECRET_KEY: c2tfdGVzdF95b3VyX3N0cmlwZV9zZWNyZXRfa2V5  # sk_test_your_stripe_secret_key
  STRIPE_WEBHOOK_SECRET: d2hzZWNfeW91cl9zdHJpcGVfd2ViaG9va19zZWNyZXQ=  # whsec_your_stripe_webhook_secret
  CIRCLE_API_KEY: eW91ci1jaXJjbGUtYXBpLWtleQ==  # your-circle-api-key
  
  # Card Issuers
  MARQETA_USERNAME: eW91ci1tYXJxZXRhLXVzZXJuYW1l  # your-marqeta-username
  MARQETA_PASSWORD: eW91ci1tYXJxZXRhLXBhc3N3b3Jk  # your-marqeta-password
  GALILEO_API_LOGIN: eW91ci1nYWxpbGVvLWFwaS1sb2dpbg==  # your-galileo-api-login
  GALILEO_API_PASSWORD: eW91ci1nYWxpbGVvLWFwaS1wYXNzd29yZA==  # your-galileo-api-password
  
  # Market Data
  CMC_API_KEY: eW91ci1jb2lubWFya2V0Y2FwLWFwaS1rZXk=  # your-coinmarketcap-api-key
  COINGECKO_API_KEY: eW91ci1jb2luZ2Vja28tYXBpLWtleQ==  # your-coingecko-api-key
  
  # Exchange APIs
  BINANCE_API_KEY: eW91ci1iaW5hbmNlLWFwaS1rZXk=  # your-binance-api-key
  BINANCE_SECRET_KEY: eW91ci1iaW5hbmNlLXNlY3JldC1rZXk=  # your-binance-secret-key
  COINBASE_API_KEY: eW91ci1jb2luYmFzZS1hcGkta2V5  # your-coinbase-api-key
  COINBASE_SECRET_KEY: eW91ci1jb2luYmFzZS1zZWNyZXQta2V5  # your-coinbase-secret-key
  COINBASE_PASSPHRASE: eW91ci1jb2luYmFzZS1wYXNzcGhyYXNl  # your-coinbase-passphrase
  
  # Monitoring
  SENTRY_DSN: eW91ci1zZW50cnktZHNu  # your-sentry-dsn

---
apiVersion: v1
kind: Secret
metadata:
  name: postgres-secret
  namespace: fintech-platform
type: Opaque
data:
  POSTGRES_PASSWORD: cG9zdGdyZXM=  # postgres

---
apiVersion: v1
kind: Secret
metadata:
  name: redis-secret
  namespace: fintech-platform
type: Opaque
data:
  REDIS_PASSWORD: ""  # Empty for no password

---
# TLS Certificate Secret (replace with actual certificates)
apiVersion: v1
kind: Secret
metadata:
  name: fintech-tls
  namespace: fintech-platform
type: kubernetes.io/tls
data:
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t  # Base64 encoded certificate
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t  # Base64 encoded private key

---
# Docker Registry Secret (for private images)
apiVersion: v1
kind: Secret
metadata:
  name: docker-registry-secret
  namespace: fintech-platform
type: kubernetes.io/dockerconfigjson
data:
  .dockerconfigjson: ****************************************************************************************************************************************************************************
