# Kafka Streams в системі замовлення кави

Цей документ описує використання Kafka Streams для обробки потоків даних у системі замовлення кави.

## Огляд

Kafka Streams - це бібліотека для обробки потоків даних, яка дозволяє створювати додатки для обробки даних у реальному часі. У системі замовлення кави Kafka Streams використовується для обробки замовлень кави та підготовки їх до виконання.

## Архітектура

Система замовлення кави з Kafka Streams складається з трьох основних компонентів:

1. **Producer**: HTTP сервер, який отримує замовлення кави та надсилає їх до Kafka.
2. **Streams Processor**: Сервіс, який обробляє замовлення кави за допомогою Kafka Streams.
3. **Consumer**: Сервіс, який споживає оброблені замовлення кави з Kafka та виконує їх.

## Потоки даних

У системі замовлення кави використовуються дві теми Kafka:

1. **coffee_orders**: Тема, в яку Producer надсилає замовлення кави.
2. **processed_orders**: Тема, в яку Streams Processor надсилає оброблені замовлення кави.

## Топологія потоків

Топологія потоків у системі замовлення кави виглядає наступним чином:

```
coffee_orders -> Streams Processor -> processed_orders
```

Streams Processor виконує наступні операції:

1. Отримує замовлення кави з теми `coffee_orders`.
2. Обробляє замовлення кави, додаючи інформацію про час приготування.
3. Надсилає оброблені замовлення кави до теми `processed_orders`.

## Конфігурація

### Streams Processor

Streams Processor налаштовується за допомогою наступних параметрів:

| Параметр | Опис | Значення за замовчуванням |
|----------|------|---------------------------|
| `KAFKA_BROKERS` | Адреси Kafka брокерів | `["localhost:9092"]` |
| `KAFKA_INPUT_TOPIC` | Тема для вхідних замовлень | `"coffee_orders"` |
| `KAFKA_OUTPUT_TOPIC` | Тема для оброблених замовлень | `"processed_orders"` |
| `KAFKA_APPLICATION_ID` | Ідентифікатор додатку Kafka Streams | `"coffee-streams-app"` |
| `KAFKA_AUTO_OFFSET_RESET` | Налаштування для початкового зсуву | `"earliest"` |
| `KAFKA_PROCESSING_GUARANTEE` | Гарантія обробки повідомлень | `"at_least_once"` |

### Consumer

Consumer налаштовується за допомогою наступних параметрів:

| Параметр | Опис | Значення за замовчуванням |
|----------|------|---------------------------|
| `KAFKA_BROKERS` | Адреси Kafka брокерів | `["localhost:9092"]` |
| `KAFKA_TOPIC` | Тема для вхідних замовлень | `"coffee_orders"` |
| `KAFKA_PROCESSED_TOPIC` | Тема для оброблених замовлень | `"processed_orders"` |

## Запуск

### Запуск Streams Processor

```bash
cd streams
go mod tidy
go run main.go
```

### Запуск усіх компонентів

Для запуску всіх компонентів системи можна використовувати скрипт `run.sh` (для Linux/macOS) або `run.bat` (для Windows):

```bash
# Linux/macOS
./run.sh

# Windows
run.bat
```

## Моніторинг

Для моніторингу Kafka Streams можна використовувати стандартні інструменти моніторингу Kafka, такі як Kafka Manager, Confluent Control Center або JMX.

## Розширення

Система замовлення кави з Kafka Streams може бути розширена наступним чином:

1. **Додавання нових операцій**: Можна додати нові операції до Streams Processor, наприклад, фільтрацію замовлень, агрегацію замовлень за типом кави тощо.
2. **Додавання нових тем**: Можна додати нові теми Kafka для обробки різних типів повідомлень, наприклад, тему для скасованих замовлень, тему для завершених замовлень тощо.
3. **Масштабування**: Можна масштабувати систему, додаючи більше екземплярів Streams Processor та Consumer.

## Висновок

Kafka Streams надає потужний інструмент для обробки потоків даних у системі замовлення кави. Він дозволяє створювати складні топології потоків для обробки замовлень кави в реальному часі.
