version: '3.8'

services:
  # Main DeFi Trading Application
  defi-trading-app:
    build:
      context: .
      dockerfile: Dockerfile.prod
    image: defi-trading-backend:latest
    container_name: defi-trading-app
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "9090:9090"  # Prometheus metrics
      - "8081:8081"  # Health checks
    environment:
      - ENV=production
      - CONFIG_PATH=/app/configs/production.yaml
      - DB_HOST=postgres
      - DB_NAME=defi_trading
      - DB_USER=defi_user
      - DB_PASSWORD=${DB_PASSWORD}
      - REDIS_HOST=redis
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - KAFKA_BROKER_1=kafka-1:9092
      - KAFKA_BROKER_2=kafka-2:9092
      - KAFKA_BROKER_3=kafka-3:9092
      - KAFKA_USERNAME=${KAFKA_USERNAME}
      - KAFKA_PASSWORD=${KAFKA_PASSWORD}
      - ONE_INCH_API_KEY=${ONE_INCH_API_KEY}
      - ETHEREUM_RPC_URL=${ETHEREUM_RPC_URL}
      - ETHEREUM_WS_URL=${ETHEREUM_WS_URL}
      - BSC_RPC_URL=${BSC_RPC_URL}
      - BSC_WS_URL=${BSC_WS_URL}
      - POLYGON_RPC_URL=${POLYGON_RPC_URL}
      - POLYGON_WS_URL=${POLYGON_WS_URL}
      - SMTP_HOST=${SMTP_HOST}
      - SMTP_USERNAME=${SMTP_USERNAME}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - SLACK_WEBHOOK_URL=${SLACK_WEBHOOK_URL}
      - PAGERDUTY_INTEGRATION_KEY=${PAGERDUTY_INTEGRATION_KEY}
      - JAEGER_ENDPOINT=http://jaeger:14268/api/traces
      - BACKUP_S3_BUCKET=${BACKUP_S3_BUCKET}
    volumes:
      - ./configs:/app/configs:ro
      - ./logs:/app/logs
      - app-data:/app/data
    depends_on:
      - postgres
      - redis
      - kafka-1
      - kafka-2
      - kafka-3
    networks:
      - defi-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G

  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: defi-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=defi_trading
      - POSTGRES_USER=defi_user
      - POSTGRES_PASSWORD=${DB_PASSWORD}
      - POSTGRES_INITDB_ARGS=--auth-host=scram-sha-256
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - defi-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U defi_user -d defi_trading"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: defi-redis
    restart: unless-stopped
    command: redis-server --requirepass ${REDIS_PASSWORD} --maxmemory 1gb --maxmemory-policy allkeys-lru
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - defi-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M

  # Kafka Cluster
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: defi-zookeeper
    restart: unless-stopped
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    volumes:
      - zookeeper-data:/var/lib/zookeeper/data
      - zookeeper-logs:/var/lib/zookeeper/log
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  kafka-1:
    image: confluentinc/cp-kafka:7.4.0
    container_name: defi-kafka-1
    restart: unless-stopped
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-1:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
      KAFKA_NUM_PARTITIONS: 3
      KAFKA_DEFAULT_REPLICATION_FACTOR: 3
    volumes:
      - kafka-1-data:/var/lib/kafka/data
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  kafka-2:
    image: confluentinc/cp-kafka:7.4.0
    container_name: defi-kafka-2
    restart: unless-stopped
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 2
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-2:29092,PLAINTEXT_HOST://localhost:9093
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
    volumes:
      - kafka-2-data:/var/lib/kafka/data
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  kafka-3:
    image: confluentinc/cp-kafka:7.4.0
    container_name: defi-kafka-3
    restart: unless-stopped
    depends_on:
      - zookeeper
    environment:
      KAFKA_BROKER_ID: 3
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka-3:29092,PLAINTEXT_HOST://localhost:9094
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 3
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 2
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 3
    volumes:
      - kafka-3-data:/var/lib/kafka/data
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: defi-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus-data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G

  # Grafana Dashboard
  grafana:
    image: grafana/grafana:latest
    container_name: defi-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
      - GF_USERS_ALLOW_SIGN_UP=false
    volumes:
      - grafana-data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - defi-network
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Jaeger Tracing
  jaeger:
    image: jaegertracing/all-in-one:latest
    container_name: defi-jaeger
    restart: unless-stopped
    ports:
      - "16686:16686"
      - "14268:14268"
    environment:
      - COLLECTOR_OTLP_ENABLED=true
    volumes:
      - jaeger-data:/tmp
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M

  # Nginx Load Balancer
  nginx:
    image: nginx:alpine
    container_name: defi-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx-logs:/var/log/nginx
    depends_on:
      - defi-trading-app
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 256M

  # Log Aggregation
  fluentd:
    image: fluent/fluentd:v1.16-debian-1
    container_name: defi-fluentd
    restart: unless-stopped
    volumes:
      - ./logging/fluentd.conf:/fluentd/etc/fluent.conf:ro
      - ./logs:/var/log/app:ro
      - nginx-logs:/var/log/nginx:ro
    ports:
      - "24224:24224"
    networks:
      - defi-network
    deploy:
      resources:
        limits:
          cpus: '0.25'
          memory: 512M

volumes:
  postgres-data:
    driver: local
  redis-data:
    driver: local
  kafka-1-data:
    driver: local
  kafka-2-data:
    driver: local
  kafka-3-data:
    driver: local
  zookeeper-data:
    driver: local
  zookeeper-logs:
    driver: local
  prometheus-data:
    driver: local
  grafana-data:
    driver: local
  jaeger-data:
    driver: local
  nginx-logs:
    driver: local
  app-data:
    driver: local

networks:
  defi-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
