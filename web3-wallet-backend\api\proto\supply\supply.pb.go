// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v3.12.4
// source: supply.proto

package supply

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Supply status enumeration
type SupplyStatus int32

const (
	SupplyStatus_SUPPLY_STATUS_UNKNOWN   SupplyStatus = 0
	SupplyStatus_SUPPLY_STATUS_HARVESTED SupplyStatus = 1
	SupplyStatus_SUPPLY_STATUS_PROCESSED SupplyStatus = 2
	SupplyStatus_SUPPLY_STATUS_SHIPPED   SupplyStatus = 3
	SupplyStatus_SUPPLY_STATUS_DELIVERED SupplyStatus = 4
	SupplyStatus_SUPPLY_STATUS_SOLD      SupplyStatus = 5
)

// Supply represents a coffee supply entry
type Supply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id        string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Origin    string                 `protobuf:"bytes,2,opt,name=origin,proto3" json:"origin,omitempty"`
	Variety   string                 `protobuf:"bytes,3,opt,name=variety,proto3" json:"variety,omitempty"`
	Quantity  float64                `protobuf:"fixed64,4,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Unit      string                 `protobuf:"bytes,5,opt,name=unit,proto3" json:"unit,omitempty"`
	Status    SupplyStatus           `protobuf:"varint,6,opt,name=status,proto3,enum=supply.SupplyStatus" json:"status,omitempty"`
	Location  string                 `protobuf:"bytes,7,opt,name=location,proto3" json:"location,omitempty"`
	CreatedAt *timestamppb.Timestamp `protobuf:"bytes,8,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	UpdatedAt *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"`
	Events    []*SupplyEvent         `protobuf:"bytes,10,rep,name=events,proto3" json:"events,omitempty"`
}

func (x *Supply) Reset() {
	*x = Supply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_supply_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Supply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Supply) ProtoMessage() {}

func (x *Supply) ProtoReflect() protoreflect.Message {
	mi := &file_supply_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Supply event for tracking
type SupplyEvent struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id          string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	SupplyId    string                 `protobuf:"bytes,2,opt,name=supply_id,json=supplyId,proto3" json:"supply_id,omitempty"`
	EventType   string                 `protobuf:"bytes,3,opt,name=event_type,json=eventType,proto3" json:"event_type,omitempty"`
	Description string                 `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	Location    string                 `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`
	Timestamp   *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Metadata    map[string]string      `protobuf:"bytes,7,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *SupplyEvent) Reset() {
	*x = SupplyEvent{}
	if protoimpl.UnsafeEnabled {
		mi := &file_supply_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SupplyEvent) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SupplyEvent) ProtoMessage() {}

func (x *SupplyEvent) ProtoReflect() protoreflect.Message {
	mi := &file_supply_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Request messages
type CreateSupplyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Origin   string  `protobuf:"bytes,1,opt,name=origin,proto3" json:"origin,omitempty"`
	Variety  string  `protobuf:"bytes,2,opt,name=variety,proto3" json:"variety,omitempty"`
	Quantity float64 `protobuf:"fixed64,3,opt,name=quantity,proto3" json:"quantity,omitempty"`
	Unit     string  `protobuf:"bytes,4,opt,name=unit,proto3" json:"unit,omitempty"`
	Location string  `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`
}

func (x *CreateSupplyRequest) Reset() {
	*x = CreateSupplyRequest{}
}

func (x *CreateSupplyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSupplyRequest) ProtoMessage() {}

type CreateSupplyResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Supply *Supply `protobuf:"bytes,1,opt,name=supply,proto3" json:"supply,omitempty"`
}

func (x *CreateSupplyResponse) Reset() {
	*x = CreateSupplyResponse{}
}

func (x *CreateSupplyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSupplyResponse) ProtoMessage() {}

// SupplyServiceClient is the client API for SupplyService service.
type SupplyServiceClient interface {
	// Create a new supply entry
	CreateSupply(ctx context.Context, in *CreateSupplyRequest, opts ...grpc.CallOption) (*CreateSupplyResponse, error)
}

type supplyServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSupplyServiceClient(cc grpc.ClientConnInterface) SupplyServiceClient {
	return &supplyServiceClient{cc}
}

func (c *supplyServiceClient) CreateSupply(ctx context.Context, in *CreateSupplyRequest, opts ...grpc.CallOption) (*CreateSupplyResponse, error) {
	out := new(CreateSupplyResponse)
	err := c.cc.Invoke(ctx, "/supply.SupplyService/CreateSupply", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SupplyServiceServer is the server API for SupplyService service.
type SupplyServiceServer interface {
	// Create a new supply entry
	CreateSupply(context.Context, *CreateSupplyRequest) (*CreateSupplyResponse, error)
}

// UnimplementedSupplyServiceServer can be embedded to have forward compatible implementations.
type UnimplementedSupplyServiceServer struct {
}

func (*UnimplementedSupplyServiceServer) CreateSupply(context.Context, *CreateSupplyRequest) (*CreateSupplyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSupply not implemented")
}

func RegisterSupplyServiceServer(s *grpc.Server, srv SupplyServiceServer) {
	s.RegisterService(&_SupplyService_serviceDesc, srv)
}

var _SupplyService_serviceDesc = grpc.ServiceDesc{
	ServiceName: "supply.SupplyService",
	HandlerType: (*SupplyServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSupply",
			Handler:    _SupplyService_CreateSupply_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "supply.proto",
}

func _SupplyService_CreateSupply_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSupplyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SupplyServiceServer).CreateSupply(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/supply.SupplyService/CreateSupply",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SupplyServiceServer).CreateSupply(ctx, req.(*CreateSupplyRequest))
	}
	return interceptor(ctx, info, handler)
}

var file_supply_proto_msgTypes = make([]protoimpl.MessageInfo, 4)

var file_supply_proto_goTypes = []interface{}{
	(*Supply)(nil),               // 0: supply.Supply
	(*SupplyEvent)(nil),          // 1: supply.SupplyEvent
	(*CreateSupplyRequest)(nil),  // 2: supply.CreateSupplyRequest
	(*CreateSupplyResponse)(nil), // 3: supply.CreateSupplyResponse
	(*timestamppb.Timestamp)(nil), // 4: google.protobuf.Timestamp
}

var file_supply_proto_depIdxs = []int32{
	0, // 0: supply.CreateSupplyResponse.supply:type_name -> supply.Supply
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}
