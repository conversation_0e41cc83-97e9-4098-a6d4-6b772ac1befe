# Глосарій термінів

Цей документ містить визначення термінів, що використовуються в системі замовлення кави.

## A

### API (Application Programming Interface)
Інтерфейс програмування додатків, який визначає, як різні компоненти програмного забезпечення повинні взаємодіяти між собою.

### Асинхронна обробка
Метод обробки, при якому операції виконуються незалежно від основного потоку виконання, дозволяючи програмі продовжувати виконання, не чекаючи завершення операції.

## B

### Брокер
Компонент системи обміну повідомленнями, який відповідає за маршрутизацію повідомлень між виробниками та споживачами.

### Батчинг
Процес групування кількох операцій або повідомлень для обробки як єдиного блоку, що може покращити продуктивність.

## C

### CORS (Cross-Origin Resource Sharing)
Механізм, який дозволяє веб-сторінкам робити запити до API, розміщеного на іншому домені.

### Consumer (Споживач)
Компонент, який отримує та обробляє повідомлення з системи обміну повідомленнями.

## D

### Docker
Платформа для розробки, доставки та запуску додатків у контейнерах.

## E

### Ендпоінт
URL, за яким доступний певний ресурс або функціональність API.

## G

### Go (Golang)
Мова програмування, розроблена Google, яка використовується для розробки системи замовлення кави.

### Горутина
Легковагий потік виконання в Go, який дозволяє виконувати функції конкурентно.

## H

### HTTP (Hypertext Transfer Protocol)
Протокол прикладного рівня для передачі гіпертекстових документів, таких як HTML.

### HTTPS (HTTP Secure)
Захищена версія HTTP, яка використовує SSL/TLS для шифрування комунікації.

## J

### JSON (JavaScript Object Notation)
Легкий формат обміну даними, який легко читається людьми та легко аналізується машинами.

### JWT (JSON Web Token)
Відкритий стандарт для створення токенів доступу, які можуть містити твердження про автентифікацію та авторизацію.

## K

### Kafka
Розподілена платформа потокової обробки, яка використовується для обміну повідомленнями між Producer та Consumer сервісами.

### Кешування
Процес зберігання копій даних у швидкодоступному місці для покращення продуктивності.

## L

### Логування
Процес запису інформації про події, що відбуваються в системі, для подальшого аналізу.

## M

### Middleware
Програмне забезпечення, яке діє як міст між операційною системою або базою даних та додатками, особливо в мережі.

### Мікросервіси
Архітектурний стиль, при якому додаток складається з невеликих, незалежних сервісів, які спілкуються через API.

## O

### OAuth
Відкритий стандарт для авторизації, який дозволяє користувачам надавати третім сторонам доступ до своїх ресурсів без передачі облікових даних.

## P

### Producer (Виробник)
Компонент, який створює та надсилає повідомлення до системи обміну повідомленнями.

### Партиція
Розділ теми в Kafka, який дозволяє паралельну обробку повідомлень.

### Пулінг з'єднань
Техніка управління з'єднаннями, при якій з'єднання повторно використовуються замість створення нових.

## R

### REST (Representational State Transfer)
Архітектурний стиль для розробки веб-сервісів, який використовує HTTP-методи для виконання операцій над ресурсами.

### Rate Limiting (Обмеження швидкості)
Техніка контролю швидкості, з якою клієнт може робити запити до API.

## S

### Sarama
Бібліотека Go для взаємодії з Apache Kafka.

### SSL/TLS
Протоколи шифрування, які забезпечують безпечну комунікацію через мережу.

## T

### Тема (Topic)
Категорія або потік повідомлень в Kafka, до якої можуть публікувати виробники та з якої можуть споживати споживачі.

### Трасування
Процес збору інформації про виконання програми для аналізу продуктивності та діагностики проблем.

## V

### Валідація
Процес перевірки даних на відповідність певним критеріям перед їх обробкою.

## W

### Worker Pool
Шаблон проектування, при якому фіксована кількість робочих потоків обробляє завдання з черги.

## Z

### ZooKeeper
Сервіс координації для розподілених систем, який використовується Kafka для управління брокерами.
