apiVersion: apps/v1
kind: Deployment
metadata:
  name: wallet-service
  namespace: web3-wallet
spec:
  replicas: 2
  selector:
    matchLabels:
      app: wallet-service
  template:
    metadata:
      labels:
        app: wallet-service
    spec:
      containers:
      - name: wallet-service
        image: yourusername/web3-wallet-wallet-service:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 50051
          name: grpc
        envFrom:
        - configMapRef:
            name: web3-wallet-config
        - secretRef:
            name: web3-wallet-secret
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50051
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 6
        readinessProbe:
          exec:
            command:
            - grpc_health_probe
            - -addr=localhost:50051
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
---
apiVersion: v1
kind: Service
metadata:
  name: wallet-service
  namespace: web3-wallet
spec:
  selector:
    app: wallet-service
  ports:
  - port: 50051
    targetPort: 50051
    name: grpc
  type: ClusterIP
